{"name": "sqleditor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "cleanup-db": "bun run scripts/cleanup-database.ts"}, "dependencies": {"@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@neondatabase/serverless": "^1.0.1", "@opentelemetry/api": "^1.9.0", "@types/bcryptjs": "^3.0.0", "ai": "^4.3.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "lucide-react": "^0.514.0", "next": "15.3.3", "next-themes": "^0.4.6", "openai": "^5.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}